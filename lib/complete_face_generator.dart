import 'dart:math';
import 'package:flutter/material.dart';
import 'widgets/face_shape.dart';
import 'widgets/eye_shape.dart';
import 'widgets/mouth_shape.dart';
import 'widgets/hair_lines.dart';

/// Avatar shape enumeration
enum AvatarShape {
  square,
  circle,
}

/// Pre-calculated random values for consistent rendering
class RandomRenderData {
  final List<double> hairStrokeWidths;
  final List<List<double>> pupilRadii;
  final List<List<Point2D>> pupilOffsets;
  final List<double> noseRadii;
  final List<Point2D> noseOffsets;
  final double mouthStrokeWidth;

  const RandomRenderData({
    required this.hairStrokeWidths,
    required this.pupilRadii,
    required this.pupilOffsets,
    required this.noseRadii,
    required this.noseOffsets,
    required this.mouthStrokeWidth,
  });
}

/// Complete face data structure
class CompleteFaceData {
  final FaceContour faceContour;
  final BothEyePoints eyes;
  final MouthPoints mouth;
  final List<List<Point2D>> hairLines;
  final Color backgroundColor;
  final Color hairColor;
  final bool haventSleptForDays;
  final double faceScale;
  final FacePositioning positioning;
  final NoseData noseData;
  final RandomRenderData randomData;
  final AvatarShape avatarShape;

  const CompleteFaceData({
    required this.faceContour,
    required this.eyes,
    required this.mouth,
    required this.hairLines,
    required this.backgroundColor,
    required this.hairColor,
    required this.haventSleptForDays,
    required this.faceScale,
    required this.positioning,
    required this.noseData,
    required this.randomData,
    required this.avatarShape,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompleteFaceData &&
        other.faceContour == faceContour &&
        other.eyes == eyes &&
        other.mouth == mouth &&
        other.backgroundColor == backgroundColor &&
        other.hairColor == hairColor &&
        other.haventSleptForDays == haventSleptForDays &&
        other.faceScale == faceScale &&
        other.positioning == positioning &&
        other.noseData == noseData &&
        other.avatarShape == avatarShape;
  }

  @override
  int get hashCode {
    return Object.hash(
      faceContour,
      eyes,
      mouth,
      backgroundColor,
      hairColor,
      haventSleptForDays,
      faceScale,
      positioning,
      noseData,
      avatarShape,
    );
  }
}

/// Face positioning data (eye positions, etc.)
class FacePositioning {
  final double distanceBetweenEyes;
  final double leftEyeOffsetX;
  final double leftEyeOffsetY;
  final double rightEyeOffsetX;
  final double rightEyeOffsetY;
  final double eyeHeightOffset;
  final Point2D leftPupilShift;
  final Point2D rightPupilShift;
  
  const FacePositioning({
    required this.distanceBetweenEyes,
    required this.leftEyeOffsetX,
    required this.leftEyeOffsetY,
    required this.rightEyeOffsetX,
    required this.rightEyeOffsetY,
    required this.eyeHeightOffset,
    required this.leftPupilShift,
    required this.rightPupilShift,
  });
}

/// Nose data
class NoseData {
  final bool isPointNose;
  final Point2D leftNoseCenter;
  final Point2D rightNoseCenter;
  
  const NoseData({
    required this.isPointNose,
    required this.leftNoseCenter,
    required this.rightNoseCenter,
  });
}

/// Color palettes (from Vue component)
class ColorPalettes {
  static const List<Color> hairColors = [
    Color(0xFF000000), // Black
    Color(0xFF2C222B), // Dark Brown
    Color(0xFF504444), // Medium Brown
    Color(0xFFA7856A), // Light Brown
    Color(0xFFDCD0BA), // Blond
    Color(0xFFE9ECEF), // Platinum Blond
    Color(0xFFA52A2A), // Red
    Color(0xFF91553D), // Auburn
    Color(0xFF808080), // Grey
    Color(0xFFB93737), // Fire
  ];
  
  static const List<Color> backgroundColors = [
    Color(0xFFF5F5DC), // Soft Beige
    Color(0xFFB0E0E6), // Pale Blue
    Color(0xFFD3D3D3), // Light Grey
    Color(0xFF98FB98), // Pastel Green
    Color(0xFFFFFDD0), // Cream
    Color(0xFFE6E6FA), // Muted Lavender
    Color(0xFFBC8F8F), // Dusty Rose
    Color(0xFF87CEEB), // Sky Blue
    Color(0xFFF5FFFA), // Mint Cream
    Color(0xFFF5DEB3), // Wheat
  ];
}

/// Custom painter for the complete face
class CompleteFacePainter extends CustomPainter {
  final CompleteFaceData faceData;

  CompleteFacePainter({required this.faceData});

  @override
  void paint(Canvas canvas, Size size) {
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double scale = (size.width / 400) * faceData.faceScale;

    // Apply clipping based on avatar shape
    _applyClipping(canvas, size);

    // Draw background
    final Paint backgroundPaint = Paint()..color = faceData.backgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);

    // Draw face contour
    _drawFaceContour(canvas, centerX, centerY, scale);

    // Draw hair
    _drawHair(canvas, centerX, centerY, scale);

    // Draw eyes
    _drawEyes(canvas, centerX, centerY, scale);

    // Draw nose
    _drawNose(canvas, centerX, centerY, scale);

    // Draw mouth
    _drawMouth(canvas, centerX, centerY, scale);
  }

  void _applyClipping(Canvas canvas, Size size) {
    final Path clipPath = Path();

    switch (faceData.avatarShape) {
      case AvatarShape.circle:
        final double radius = size.width / 2;
        clipPath.addOval(Rect.fromCircle(
          center: Offset(size.width / 2, size.height / 2),
          radius: radius,
        ));
        break;
      case AvatarShape.square:
        clipPath.addRect(Rect.fromLTWH(0, 0, size.width, size.height));
        break;
    }

    canvas.clipPath(clipPath);
  }
  
  void _drawFaceContour(Canvas canvas, double centerX, double centerY, double scale) {
    final Paint facePaint = Paint()
      ..color = const Color(0xFFFFC9A9) // Skin color
      ..style = PaintingStyle.fill;

    final Paint strokePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0 / faceData.faceScale
      ..strokeJoin = StrokeJoin.round;

    final Path facePath = Path();
    if (faceData.faceContour.points.isNotEmpty) {
      final Point2D firstPoint = faceData.faceContour.points.first;
      facePath.moveTo(
        centerX + firstPoint.x * scale,
        centerY + firstPoint.y * scale, // Use normal Y coordinate
      );

      for (int i = 1; i < faceData.faceContour.points.length; i++) {
        final Point2D point = faceData.faceContour.points[i];
        facePath.lineTo(
          centerX + point.x * scale,
          centerY + point.y * scale, // Use normal Y coordinate
        );
      }
      facePath.close();
    }

    canvas.drawPath(facePath, facePaint);
    canvas.drawPath(facePath, strokePaint);
  }
  
  void _drawHair(Canvas canvas, double centerX, double centerY, double scale) {
    for (int lineIndex = 0; lineIndex < faceData.hairLines.length; lineIndex++) {
      final List<Point2D> hairLine = faceData.hairLines[lineIndex];
      if (hairLine.isNotEmpty && lineIndex < faceData.randomData.hairStrokeWidths.length) {
        final Paint hairPaint = Paint()
          ..color = faceData.hairColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = faceData.randomData.hairStrokeWidths[lineIndex]
          ..strokeJoin = StrokeJoin.round;

        final Path hairPath = Path();
        final Point2D firstPoint = hairLine.first;
        hairPath.moveTo(
          centerX + firstPoint.x * scale,
          centerY + firstPoint.y * scale,
        );

        for (int i = 1; i < hairLine.length; i++) {
          final Point2D point = hairLine[i];
          hairPath.lineTo(
            centerX + point.x * scale,
            centerY + point.y * scale,
          );
        }

        canvas.drawPath(hairPath, hairPaint);
      }
    }
  }
  
  void _drawEyes(Canvas canvas, double centerX, double centerY, double scale) {
    final Paint eyePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    final Paint eyeStrokePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = (faceData.haventSleptForDays ? 5.0 : 3.0) / faceData.faceScale
      ..strokeJoin = StrokeJoin.round
      ..strokeCap = StrokeCap.round;
    
    // Draw left eye
    _drawSingleEye(canvas, centerX, centerY, scale, faceData.eyes.left, 
        faceData.positioning.leftEyeOffsetX, faceData.positioning.leftEyeOffsetY,
        faceData.positioning.leftPupilShift, eyePaint, eyeStrokePaint, true);
    
    // Draw right eye
    _drawSingleEye(canvas, centerX, centerY, scale, faceData.eyes.right,
        faceData.positioning.rightEyeOffsetX, faceData.positioning.rightEyeOffsetY,
        faceData.positioning.rightPupilShift, eyePaint, eyeStrokePaint, false);
  }
  
  void _drawSingleEye(Canvas canvas, double centerX, double centerY, double scale,
      EyePoints eye, double offsetX, double offsetY, Point2D pupilShift,
      Paint eyePaint, Paint eyeStrokePaint, bool isLeft) {

    final double eyeCenterX = isLeft
        ? centerX - (faceData.faceContour.center.x + faceData.positioning.distanceBetweenEyes + offsetX) * scale
        : centerX + (faceData.faceContour.center.x + faceData.positioning.distanceBetweenEyes + offsetX) * scale;
    final double eyeCenterY = centerY - (faceData.faceContour.center.y - faceData.positioning.eyeHeightOffset - offsetY) * scale;

    // Draw eye white background
    final Path eyeContourPath = Path();
    final List<Point2D> eyeContour = eye.upper.sublist(10, 90) + eye.lower.sublist(10, 90).reversed.toList();

    if (eyeContour.isNotEmpty) {
      final Point2D firstPoint = eyeContour.first;
      eyeContourPath.moveTo(
        eyeCenterX + firstPoint.x * scale,
        eyeCenterY + firstPoint.y * scale,
      );

      for (int i = 1; i < eyeContour.length; i++) {
        final Point2D point = eyeContour[i];
        eyeContourPath.lineTo(
          eyeCenterX + point.x * scale,
          eyeCenterY + point.y * scale,
        );
      }
      eyeContourPath.close();
    }

    canvas.drawPath(eyeContourPath, eyePaint);

    // Draw upper eyelid
    _drawEyelid(canvas, eyeCenterX, eyeCenterY, scale, eye.upper, eyeStrokePaint);

    // Draw lower eyelid
    _drawEyelid(canvas, eyeCenterX, eyeCenterY, scale, eye.lower, eyeStrokePaint);

    // Draw pupils (circles) using pre-calculated values
    final Paint pupilPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final int eyeIndex = isLeft ? 0 : 1;
    if (eyeIndex < faceData.randomData.pupilRadii.length &&
        eyeIndex < faceData.randomData.pupilOffsets.length) {
      final List<double> radii = faceData.randomData.pupilRadii[eyeIndex];
      final List<Point2D> offsets = faceData.randomData.pupilOffsets[eyeIndex];

      for (int i = 0; i < radii.length && i < offsets.length; i++) {
        final double radius = radii[i];
        final Point2D offset = offsets[i];
        final double pupilX = eyeCenterX + (pupilShift.x + offset.x) * scale;
        final double pupilY = eyeCenterY + (pupilShift.y + offset.y) * scale;

        canvas.drawCircle(Offset(pupilX, pupilY), radius, pupilPaint);
      }
    }
  }
  
  void _drawEyelid(Canvas canvas, double eyeCenterX, double eyeCenterY, double scale,
      List<Point2D> eyelidPoints, Paint paint) {
    if (eyelidPoints.isNotEmpty) {
      final Path eyelidPath = Path();
      final Point2D firstPoint = eyelidPoints.first;
      eyelidPath.moveTo(
        eyeCenterX + firstPoint.x * scale,
        eyeCenterY + firstPoint.y * scale,
      );

      for (int i = 1; i < eyelidPoints.length; i++) {
        final Point2D point = eyelidPoints[i];
        eyelidPath.lineTo(
          eyeCenterX + point.x * scale,
          eyeCenterY + point.y * scale,
        );
      }

      canvas.drawPath(eyelidPath, paint);
    }
  }
  
  void _drawNose(Canvas canvas, double centerX, double centerY, double scale) {
    final Paint nosePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    if (faceData.noseData.isPointNose) {
      // Draw point nose (circles) using pre-calculated values
      final List<double> radii = faceData.randomData.noseRadii;
      final List<Point2D> offsets = faceData.randomData.noseOffsets;

      for (int i = 0; i < radii.length && i < offsets.length; i += 2) {
        if (i + 1 < radii.length && i + 1 < offsets.length) {
          final double radius = radii[i];
          final Point2D rightOffset = offsets[i];
          final Point2D leftOffset = offsets[i + 1];

          // Right nostril
          final double rightX = centerX + (faceData.noseData.rightNoseCenter.x + rightOffset.x) * scale;
          final double rightY = centerY + (faceData.noseData.rightNoseCenter.y + rightOffset.y) * scale;
          canvas.drawCircle(Offset(rightX, rightY), radius, nosePaint);

          // Left nostril
          final double leftX = centerX + (faceData.noseData.leftNoseCenter.x + leftOffset.x) * scale;
          final double leftY = centerY + (faceData.noseData.leftNoseCenter.y + leftOffset.y) * scale;
          canvas.drawCircle(Offset(leftX, leftY), radius, nosePaint);
        }
      }
    } else {
      // Draw line nose (curved path) - no random values needed here
      final Paint lineNosePaint = Paint()
        ..color = Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.5
        ..strokeJoin = StrokeJoin.round;

      final Path nosePath = Path();
      final double leftX = centerX + faceData.noseData.leftNoseCenter.x * scale;
      final double leftY = centerY + faceData.noseData.leftNoseCenter.y * scale;
      final double rightX = centerX + faceData.noseData.rightNoseCenter.x * scale;
      final double rightY = centerY + faceData.noseData.rightNoseCenter.y * scale;
      final double midX = centerX + (faceData.noseData.leftNoseCenter.x + faceData.noseData.rightNoseCenter.x) / 2 * scale;
      final double midY = centerY - faceData.positioning.eyeHeightOffset * 0.2 * scale;

      nosePath.moveTo(leftX, leftY);
      nosePath.quadraticBezierTo(rightX, rightY * 1.5, midX, midY);

      canvas.drawPath(nosePath, lineNosePaint);
    }
  }
  
  void _drawMouth(Canvas canvas, double centerX, double centerY, double scale) {
    final Paint mouthPaint = Paint()
      ..color = const Color(0xFFD77F8C) // rgb(215,127,140)
      ..style = PaintingStyle.fill;

    final Paint mouthStrokePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = faceData.randomData.mouthStrokeWidth
      ..strokeJoin = StrokeJoin.round;

    if (faceData.mouth.points.isNotEmpty) {
      final Path mouthPath = Path();
      final Point2D firstPoint = faceData.mouth.points.first;
      mouthPath.moveTo(
        centerX + firstPoint.x * scale,
        centerY + firstPoint.y * scale,
      );

      for (int i = 1; i < faceData.mouth.points.length; i++) {
        final Point2D point = faceData.mouth.points[i];
        mouthPath.lineTo(
          centerX + point.x * scale,
          centerY + point.y * scale,
        );
      }
      mouthPath.close();

      canvas.drawPath(mouthPath, mouthPaint);
      canvas.drawPath(mouthPath, mouthStrokePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is! CompleteFacePainter) return true;
    return oldDelegate.faceData != faceData;
  }
}

/// Complete face generator widget (Flutter version of Vue component)
class CompleteFaceGeneratorWidget extends StatefulWidget {
  final double size;
  final AvatarShape initialShape;

  const CompleteFaceGeneratorWidget({
    super.key,
    this.size = 500,
    this.initialShape = AvatarShape.square,
  });

  @override
  State<CompleteFaceGeneratorWidget> createState() => _CompleteFaceGeneratorWidgetState();
}

class _CompleteFaceGeneratorWidgetState extends State<CompleteFaceGeneratorWidget> {
  late CompleteFaceData _faceData;
  late AvatarShape _currentShape;

  @override
  void initState() {
    super.initState();
    _currentShape = widget.initialShape;
    _generateNewFace();
  }

  void _generateNewFace() {
    setState(() {
      _faceData = _generateCompleteFace();
    });
  }

  void _toggleShape() {
    setState(() {
      _currentShape = _currentShape == AvatarShape.square
          ? AvatarShape.circle
          : AvatarShape.square;
      // Update the face data with new shape
      _faceData = CompleteFaceData(
        faceContour: _faceData.faceContour,
        eyes: _faceData.eyes,
        mouth: _faceData.mouth,
        hairLines: _faceData.hairLines,
        backgroundColor: _faceData.backgroundColor,
        hairColor: _faceData.hairColor,
        haventSleptForDays: _faceData.haventSleptForDays,
        faceScale: _faceData.faceScale,
        positioning: _faceData.positioning,
        noseData: _faceData.noseData,
        randomData: _faceData.randomData,
        avatarShape: _currentShape,
      );
    });
  }

  CompleteFaceData _generateCompleteFace() {
    // Generate face scale
    final double faceScale = 1.5 + Random().nextDouble() * 0.6;
    final bool haventSleptForDays = Random().nextDouble() > 0.8;

    // Generate face contour
    final FaceContour faceContour = FaceShapeGenerator.generateFaceContourPoints();

    // Generate eyes
    final BothEyePoints eyes = EyeShapeGenerator.generateBothEyes(width: faceContour.width / 2);

    // Generate face positioning
    final FacePositioning positioning = _generateFacePositioning(faceContour, eyes);

    // Generate hair
    final List<List<Point2D>> hairLines = HairLinesGenerator.generateRandomHair(faceContour.points);

    // Generate nose
    final NoseData noseData = _generateNoseData(faceContour);

    // Generate mouth
    final MouthParameters mouthParams = MouthParameters(
      faceHeight: faceContour.height,
      faceWidth: faceContour.width,
      faceContour: faceContour.points,
    );
    final MouthPoints mouth = MouthShapeGenerator.generateRandomMouth(mouthParams);

    // Choose colors
    final Color backgroundColor = ColorPalettes.backgroundColors[
        Random().nextInt(ColorPalettes.backgroundColors.length)];

    Color hairColor;
    if (Random().nextDouble() > 0.1) {
      // Use natural hair color
      hairColor = ColorPalettes.hairColors[Random().nextInt(min(10, ColorPalettes.hairColors.length))];
    } else {
      // Use rainbow/colorful hair (simplified - just pick a random bright color)
      hairColor = Color.fromARGB(255, Random().nextInt(256), Random().nextInt(256), Random().nextInt(256));
    }

    // Generate random render data
    final RandomRenderData randomData = _generateRandomRenderData(hairLines.length);

    return CompleteFaceData(
      faceContour: faceContour,
      eyes: eyes,
      mouth: mouth,
      hairLines: hairLines,
      backgroundColor: backgroundColor,
      hairColor: hairColor,
      haventSleptForDays: haventSleptForDays,
      faceScale: faceScale,
      positioning: positioning,
      noseData: noseData,
      randomData: randomData,
      avatarShape: _currentShape,
    );
  }

  RandomRenderData _generateRandomRenderData(int hairLineCount) {
    final Random random = Random();

    // Generate hair stroke widths
    final List<double> hairStrokeWidths = List.generate(
      hairLineCount,
      (index) => 0.5 + random.nextDouble() * 2.5,
    );

    // Generate pupil data for both eyes (left and right)
    final List<List<double>> pupilRadii = List.generate(2, (eyeIndex) {
      return List.generate(10, (index) => random.nextDouble() * 2 + 3.0);
    });

    final List<List<Point2D>> pupilOffsets = List.generate(2, (eyeIndex) {
      return List.generate(10, (index) => Point2D(
        random.nextDouble() * 5 - 2.5,
        random.nextDouble() * 5 - 2.5,
      ));
    });

    // Generate nose data (20 values for 10 circles, 2 per circle)
    final List<double> noseRadii = List.generate(10, (index) => random.nextDouble() * 2 + 1.0);
    final List<Point2D> noseOffsets = List.generate(20, (index) => Point2D(
      random.nextDouble() * 4 - 2,
      random.nextDouble() * 4 - 2,
    ));

    // Generate mouth stroke width
    final double mouthStrokeWidth = 2.7 + random.nextDouble() * 0.5;

    return RandomRenderData(
      hairStrokeWidths: hairStrokeWidths,
      pupilRadii: pupilRadii,
      pupilOffsets: pupilOffsets,
      noseRadii: noseRadii,
      noseOffsets: noseOffsets,
      mouthStrokeWidth: mouthStrokeWidth,
    );
  }

  FacePositioning _generateFacePositioning(FaceContour faceContour, BothEyePoints eyes) {
    final double distanceBetweenEyes = RandomUtils.randomFromInterval(
        faceContour.width / 4.5, faceContour.width / 4);
    final double eyeHeightOffset = RandomUtils.randomFromInterval(
        faceContour.height / 8, faceContour.height / 6);

    final double leftEyeOffsetX = RandomUtils.randomFromInterval(
        -faceContour.width / 20, faceContour.width / 10);
    final double leftEyeOffsetY = RandomUtils.randomFromInterval(
        -faceContour.height / 50, faceContour.height / 50);
    final double rightEyeOffsetX = RandomUtils.randomFromInterval(
        -faceContour.width / 20, faceContour.width / 10);
    final double rightEyeOffsetY = RandomUtils.randomFromInterval(
        -faceContour.height / 50, faceContour.height / 50);

    // Generate pupil shifts (simplified version of the complex Vue logic)
    final Point2D leftPupilShift = Point2D(
      RandomUtils.randomFromInterval(-faceContour.width / 20, faceContour.width / 20),
      RandomUtils.randomFromInterval(-faceContour.height / 20, faceContour.height / 20),
    );
    final Point2D rightPupilShift = Point2D(
      RandomUtils.randomFromInterval(-faceContour.width / 20, faceContour.width / 20),
      RandomUtils.randomFromInterval(-faceContour.height / 20, faceContour.height / 20),
    );

    return FacePositioning(
      distanceBetweenEyes: distanceBetweenEyes,
      leftEyeOffsetX: leftEyeOffsetX,
      leftEyeOffsetY: leftEyeOffsetY,
      rightEyeOffsetX: rightEyeOffsetX,
      rightEyeOffsetY: rightEyeOffsetY,
      eyeHeightOffset: eyeHeightOffset,
      leftPupilShift: leftPupilShift,
      rightPupilShift: rightPupilShift,
    );
  }

  NoseData _generateNoseData(FaceContour faceContour) {
    final bool isPointNose = Random().nextDouble() > 0.5;

    final Point2D rightNoseCenter = Point2D(
      RandomUtils.randomFromInterval(faceContour.width / 18, faceContour.width / 12),
      RandomUtils.randomFromInterval(0, faceContour.height / 5),
    );

    final Point2D leftNoseCenter = Point2D(
      RandomUtils.randomFromInterval(-faceContour.width / 18, -faceContour.width / 12),
      rightNoseCenter.y + RandomUtils.randomFromInterval(-faceContour.height / 30, faceContour.height / 20),
    );

    return NoseData(
      isPointNose: isPointNose,
      leftNoseCenter: leftNoseCenter,
      rightNoseCenter: rightNoseCenter,
    );
  }

  void _downloadFace() {
    // TODO: Implement download functionality
    // This would involve converting the CustomPainter to an image and saving it
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Download functionality coming soon!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [

          // Face display
          Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.black, width: 2),
              borderRadius: _currentShape == AvatarShape.circle
                  ? BorderRadius.circular(widget.size / 2)
                  : BorderRadius.circular(5),
            ),
            child: ClipRRect(
              borderRadius: _currentShape == AvatarShape.circle
                  ? BorderRadius.circular(widget.size / 2)
                  : BorderRadius.circular(3),
              child: CustomPaint(
                painter: CompleteFacePainter(faceData: _faceData),
                size: Size(widget.size, widget.size),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                'ANOTHER',
                _generateNewFace,
                Colors.blue,
              ),
              _buildControlButton(
                _currentShape == AvatarShape.square ? 'CIRCLE' : 'SQUARE',
                _toggleShape,
                Colors.orange,
              ),
              _buildControlButton(
                'DOWNLOAD',
                _downloadFace,
                Colors.green,
              ),
            ],
          ),

          const SizedBox(height: 10),

          // Face info
          Text(
            'Scale: ${_faceData.faceScale.toStringAsFixed(2)} | '
            'Tired: ${_faceData.haventSleptForDays ? "Yes" : "No"} | '
            'Shape: ${_currentShape == AvatarShape.square ? "Square" : "Circle"}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton(String text, VoidCallback onPressed, Color color) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black,
        side: const BorderSide(color: Colors.black, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        shadowColor: Colors.black,
        elevation: 2,
      ).copyWith(
        backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.hovered)) {
            return Colors.black;
          }
          if (states.contains(WidgetState.pressed)) {
            return Colors.grey[700]!;
          }
          return Colors.transparent;
        }),
        foregroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.hovered)) {
            return Colors.white;
          }
          if (states.contains(WidgetState.pressed)) {
            return Colors.white;
          }
          return Colors.black;
        }),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
