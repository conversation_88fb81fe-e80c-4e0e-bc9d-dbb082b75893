import 'package:flutter/material.dart';
import 'complete_face_generator.dart';

void main() {
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ugly Avatar - Shape Generator',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const UglyAvatarDemo(),
    );
  }
}

class UglyAvatarDemo extends StatelessWidget {
  const UglyAvatarDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ugly Avatar - Shape Generator'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Ugly Avatar Shape Generator',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 10),
            

              // Complete Face Generator Section
              Text(
                '🎭 Complete Face Generator',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple,
                ),
              ),
              CompleteFaceGeneratorWidget(size: 400),
              SizedBox(height: 40),
 
            ],
          ),
        ),
      ),
    );
  }
}
