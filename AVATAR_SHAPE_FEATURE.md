# Avatar Shape Control Feature

## 概述

为 Ugly Avatar 项目添加了头像形状控制功能，现在支持正方形和圆形两种头像形状。

## 新增功能

### 1. 头像形状枚举
```dart
enum AvatarShape {
  square,  // 正方形
  circle,  // 圆形
}
```

### 2. 更新的数据结构
- `CompleteFaceData` 类新增了 `avatarShape` 参数
- 支持在创建头像时指定形状

### 3. 动态形状切换
- 添加了形状切换按钮（CIRCLE/SQUARE）
- 可以在运行时动态切换头像形状
- 容器边框会根据选择的形状自动调整

### 4. 视觉效果
- **正方形**：使用圆角矩形边框
- **圆形**：使用完全圆形的边框和裁剪
- 头像内容会根据形状进行相应的裁剪

## 使用方法

### 基本用法
```dart
// 默认正方形头像
CompleteFaceGeneratorWidget(
  size: 400,
)

// 指定初始形状为圆形
CompleteFaceGeneratorWidget(
  size: 400,
  initialShape: AvatarShape.circle,
)
```

### 控制按钮
- **ANOTHER**: 生成新的头像
- **CIRCLE/SQUARE**: 切换头像形状（按钮文字会根据当前形状动态变化）
- **DOWNLOAD**: 下载头像（功能待实现）

### 状态信息
头像下方会显示当前状态信息：
- Scale: 头像缩放比例
- Tired: 是否疲惫状态
- Shape: 当前头像形状（Square/Circle）

## 技术实现

### 1. 裁剪实现
在 `CompleteFacePainter` 中使用 `Canvas.clipPath()` 方法：
- 圆形：创建椭圆形裁剪路径
- 正方形：创建矩形裁剪路径

### 2. 容器装饰
使用 `ClipRRect` 和 `BoxDecoration` 来实现容器的视觉效果：
- 动态调整 `borderRadius`
- 保持边框和内容的一致性

### 3. 状态管理
- 使用 `_currentShape` 状态变量跟踪当前形状
- `_toggleShape()` 方法处理形状切换逻辑

## 示例代码

查看 `example/avatar_shape_demo.dart` 文件获取完整的使用示例。

## 兼容性

- 保持向后兼容，默认形状为正方形
- 现有代码无需修改即可继续使用
- 新功能为可选参数，不影响现有功能

## 测试

运行以下命令进行代码分析：
```bash
flutter analyze lib/
```

所有修改都通过了静态分析，只有少量无关紧要的警告。
