import 'package:flutter/material.dart';
import '../lib/complete_face_generator.dart';

/// Demo app showing avatar shape functionality
class AvatarShapeDemo extends StatelessWidget {
  const AvatarShapeDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Avatar Shape Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const AvatarShapeDemoPage(),
    );
  }
}

class AvatarShapeDemoPage extends StatelessWidget {
  const AvatarShapeDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Avatar Shape Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Avatar Shape Control Demo',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20),
              
              Text(
                'Default Square Avatar',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              SizedBox(height: 10),
              CompleteFaceGeneratorWidget(
                size: 300,
                initialShape: AvatarShape.square,
              ),
              
              SizedBox(height: 40),
              
              Text(
                'Default Circle Avatar',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              SizedBox(height: 10),
              CompleteFaceGeneratorWidget(
                size: 300,
                initialShape: AvatarShape.circle,
              ),
              
              SizedBox(height: 20),
              
              Text(
                'Click the CIRCLE/SQUARE button to toggle between shapes!',
                style: TextStyle(
                  fontSize: 16,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void main() {
  runApp(const AvatarShapeDemo());
}
